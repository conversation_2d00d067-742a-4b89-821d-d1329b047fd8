## 销售调价单保存接口

### 接口描述：

- 广交云.【销售调价单】.审核后 **推送** 金蝶云·星空旗舰版.【销售调价单】

**请求 URL**：

{{http/https}}://{{localhost}}/kapi/v2/gjwl/im/gjwl_salpriceadjust/saveSalPriceAdjust

**请求 URL（沙箱环境）:**

[https://gjwl.test.kdgalaxy.com/kapi/v2/gjwl/im/gjwl_salpriceadjust/saveSalPriceAdjust](https://gjwl.test.kdgalaxy.com/kapi/v2/gjwl/cas/recClaimInterface/recClaimInterface)

**调用方式**：HTTP调用

**请求方式**：POST

**请求类型**：Content-Type:application/json

**请求Header参数：(和 [3.1.客户保存提交接口](#_客户保存提交接口) 请求Header参数的一致)**

**请求Body参数：**

|     |     |     |     |     |     |
| --- | --- | --- | --- | --- | --- |
| **参数名** | **参数类型** | **是否必填** | **描述说明** | **层级** | **参数值** |
| id  | String | 否   | id  | 1   | 修改时需传 |
| billno | String | 是   | 单据编号 | 1   | "1164021" |
| org_number | String | 是   | 组织.编码 | 1   | 固定值，传"1011" |
| gjwl_date | Date | 是   | 业务日期 | 1   | "2025-09-12" |
| gjwl_customer_number | String | 是   | 客户.编码 | 1   | "Cus-0000027" |
| gjwl_reason | String | 是   | 调价原因 | 1   |     |
| gjwl_thirdparty_billno | String | 是   | 外部系统单号 | 1   | "1164021" |
| gjwl_sourcesystemtype | String | 是   | 来源系统 | 1   | "广交云供应链管理系统" |
| entryentity | Entries | 否   | 物料明细 | 1   | {},{} |
| id  | Long | 否   | 物料明细.id | 2   | 修改时需传 |
| gjwl_salorderbillno | String | 否   | 物料明细.销售单号 | 2   | "1164026" |
| gjwl_billno | String | 否   | 物料明细.单据编号 | 2   | "1142227" |
| gjwl_billtype | String | 否   | 物料明细.单据类型 | 2   | "销售出库单" |
| gjwl_materiel_number | String | 否   | 物料编码.编码 | 2   | "*********" |
| gjwl_product_lotno | String | 否   | 物料明细.生产批号/序列号 | 2   | "5757" |
| gjwl_taxrate_number | String | 是   | 税率.编码 | 2   | "001005" |
| gjwl_qty | Decimal | 否   | 物料明细.数量 | 2   | "1" |
| gjwl_oldtaxprice | Decimal | 否   | 物料明细.原单价（含税） | 2   | "12" |
| gjwl_newesttaxprice | Decimal | 否   | 物料明细.调整前最新价（含税） | 2   | "12" |
| gjwl_taxprice | Decimal | 否   | 物料明细.本次调整单价（含税） | 2   | "11" |
| gjwl_adjustamount | Decimal | 否   | 物料明细.调整金额 | 2   | "-1" |

**请求参数示例（沙箱环境）:**

{

&nbsp;   "data":\[

&nbsp;       {

&nbsp;           "billno":"1164020",

&nbsp;           "org_number":"1011",

&nbsp;           "gjwl_date":"2025-09-19",

&nbsp;           "gjwl_customer_number":"Cus-0000027",

&nbsp;           "gjwl_reason":"对接测试",

&nbsp;           "gjwl_thirdparty_billno":"1164020",

&nbsp;           "gjwl_sourcesystemtype":"广交云供应链管理系统",

&nbsp;           "entryentity":\[

&nbsp;               {

&nbsp;                   "gjwl_salorderbillno":"1164026",

&nbsp;                   "gjwl_billno":"1142227",

&nbsp;                   "gjwl_billtype":"销售出库单",

&nbsp;                   "gjwl_materiel_number":"*********",

&nbsp;                   "gjwl_product_lotno":"5757",

&nbsp;                   "gjwl_taxrate_number":"001005",

&nbsp;                   "gjwl_qty":"1",

&nbsp;                   "gjwl_oldtaxprice":"12",

&nbsp;                   "gjwl_newesttaxprice":"12",

&nbsp;                   "gjwl_taxprice":"11",

&nbsp;                   "gjwl_adjustamount":"-1"

&nbsp;               }

&nbsp;           \]

&nbsp;       }

&nbsp;   \]

}

**返回参数：**

|     |     |     |     |     |
| --- | --- | --- | --- | --- |
| **参数名** | **参数值** | **参数类型** | **层级** | **描述说明** |
| data | {}  | Object | 1   | 结果数据 |
| result | "\[\]" | Array&lt;Map&gt; | 2   | 返回结果详细信息 |
| failCount | "2" | String | 2   | 操作失败数量 |
| successCount | "1" | String | 2   | 操作成功数量 |
| errorCode | 成功时为0，失败时会返回错误码如400 | String | 1   | 错误码 |
| message | 成功时为空，失败时会返回错误信息如“操作失败” | String | 1   | 接口调用错误信息 |
| status | true/false | Boolean | 1   | 接口访问是否成功 |

**返回参数示例:**

{

&nbsp;   "data": {

&nbsp;       "failCount": "0",

&nbsp;       "result": \[

&nbsp;           {

&nbsp;               "billIndex": 0,

&nbsp;               "billStatus": **true**,

&nbsp;               "errors": \[\],

&nbsp;               "id": "2307399879703413760",

&nbsp;               "keys": {

&nbsp;                   "id": ""

&nbsp;               },

&nbsp;               "number": "1164020",

&nbsp;               "type": "Add"

&nbsp;           }

&nbsp;       \],

&nbsp;       "successCount": "1"

&nbsp;   },

&nbsp;   "errorCode": "0",

&nbsp;   "message": **null**,

&nbsp;   "status": **true**

}
