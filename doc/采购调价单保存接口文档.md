## 采购调价单保存接口

### 接口描述：

- 广交云.【入库调价单】.审核后 **推送** 金蝶云·星空旗舰版.【采购调价单】

**请求 URL**：

{{http/https}}://{{localhost}}/kapi/v2/gjwl/im/gjwl_purpriceadjust/savePurPriceAdjust

**请求 URL（沙箱环境）:**

[https://gjwl.test.kdgalaxy.com/kapi/v2/gjwl/im/gjwl_purpriceadjust/savePurPriceAdjust](https://gjwl.test.kdgalaxy.com/kapi/v2/gjwl/cas/recClaimInterface/recClaimInterface)

**调用方式**：HTTP调用

**请求方式**：POST

**请求类型**：Content-Type:application/json

**请求Header参数：(和 [3.1.客户保存提交接口](#_客户保存提交接口) 请求Header参数的一致)**

**请求Body参数：**

|     |     |     |     |        |     |
| --- | --- | --- | --- |--------| --- |
| **参数名** | **参数类型** | **是否必填** | **描述说明** | **层级** | **参数值** |
| id  | String | 否   | id  | 1      | 修改时需传 |
| billno | String | 是   | 单据编号 | 1      | "1164021" |
| org_number | String | 是   | 组织.编码 | 1      | 固定值，传"1011" |
| gjwl_date | Date | 是   | 业务日期 | 1      | "2025-09-12" |
| gjwl_type | String | 是   | 调价类型<br><br>0：入库调价<br><br>1：返利调价 | 1      | "0" |
| gjwl_reason | String | 是   | 调价原因 | 1      | "采购调价" |
| gjwl_thirdparty_billno | String | 是   | 外部系统单号 | 1      | "1164021" |
| gjwl_sourcesystemtype | String | 是   | 来源系统 | 1      | "广交云供应链管理系统" |
| gjwl_supplier_number | String | 是   | 供应商.编码 | 1      | "QX000004" |
| entryentity | Entries | 否   | 物料明细 | 1      | {},{} |
| id  | Long | 否   | 物料明细.id | 2      | 修改时需传 |
| gjwl_instockbillno | String | 是   | 采购入库单号 | 2      | "2164091" |
| gjwl_materiel_masterid_number | String | 是   | 物料编码.编码 | 2      | "*********" |
| gjwl_producer | String | 否   | 物料明细.生产厂家/受托生产企业 | 2      | "xxx公司" |
| gjwl_qty | Decimal | 否   | 物料明细.数量 | 2      | "10" |
| gjwl_lotno | String | 否   | 物料明细.批号 | 2      | "2128729"，对应广交云的批次 |
| gjwl_product_lotno | String | 否   | 物料明细.生产批号/序列号 | 2      | "7834325" |
| gjwl_price | Decimal | 否   | 物料明细.采购价 | 2      | "23.99" |
| gjwl_untaxamount | Decimal | 否   | 物料明细.不含税金额 | 2      | "0.00" |
| gjwl_tax | Decimal | 否   | 物料明细.税额 | 2      | "0.00" |
| gjwl_invoiceno | String | 否   | 物料明细.发票号码 | 2      | "1010191" |
| gjwl_returnqty | Decimal | 否   | 物料明细.当前退货数量 | 2      | "0" |
| gjwl_afterprice | Decimal | 否   | 物料明细.调整后采购价 | 2      | "25.99" |
| gjwl_instockdiffamount | Decimal | 否   | 物料明细.入库调整差额 | 2      | "200.00" |
| gjwl_returndiffamount | Decimal | 否   | 物料明细.采退调差金额 | 2      | "0.00" |
| gjwl_realdiffamount | Decimal | 否   | 物料明细.实际调整差额 | 2      | "200.00" |

**请求参数示例（沙箱环境）:**

{

&nbsp;   "data":\[

&nbsp;       {

&nbsp;           "billno":"1164099",

&nbsp;           "org_number":"1011",

&nbsp;           "gjwl_date":"2025-09-19",

&nbsp;           "gjwl_type":"0",

&nbsp;           "gjwl_instockbillno":"1398711",

&nbsp;           "gjwl_reason":"测试对接",

&nbsp;           "gjwl_supplier_number":"QX000004",

&nbsp;           "gjwl_sourcesystemtype":"广交云供应链管理系统",

&nbsp;           "gjwl_thirdparty_billno":"1164099",

&nbsp;           "entryentity":\[

&nbsp;               {

&nbsp;                   "gjwl_materiel_masterid_number":"*********",

&nbsp;                   "gjwl_producer":"M记食品",

&nbsp;                   "gjwl_qty":"100",

&nbsp;                   "gjwl_lotno":"2128729",

&nbsp;                   "gjwl_product_lotno":"7834325",

&nbsp;                   "gjwl_price":"23.99",

&nbsp;                   "gjwl_untaxamount":"0",

&nbsp;                   "gjwl_tax":"0",

&nbsp;                   "gjwl_invoiceno":"19191919",

&nbsp;                   "gjwl_returnqty":"0",

&nbsp;                   "gjwl_afterprice":"25.99",

&nbsp;                   "gjwl_instockdiffamount":"200.00",

&nbsp;                   "gjwl_returndiffamount":"0.00",

&nbsp;                   "gjwl_realdiffamount":"200.00"

&nbsp;               }

&nbsp;           \]

&nbsp;       }

&nbsp;   \]

}  

**返回参数：**

|     |     |     |     |     |
| --- | --- | --- | --- | --- |
| **参数名** | **参数值** | **参数类型** | **层级** | **描述说明** |
| data | {}  | Object | 1   | 结果数据 |
| result | "\[\]" | Array&lt;Map&gt; | 2   | 返回结果详细信息 |
| failCount | "2" | String | 2   | 操作失败数量 |
| successCount | "1" | String | 2   | 操作成功数量 |
| errorCode | 成功时为0，失败时会返回错误码如400 | String | 1   | 错误码 |
| message | 成功时为空，失败时会返回错误信息如“操作失败” | String | 1   | 接口调用错误信息 |
| status | true/false | Boolean | 1   | 接口访问是否成功 |

**返回参数示例:**

{

&nbsp;   "data": {

&nbsp;       "failCount": "0",

&nbsp;       "result": \[

&nbsp;           {

&nbsp;               "billIndex": 0,

&nbsp;               "billStatus": **true**,

&nbsp;               "errors": \[\],

&nbsp;               "id": "2307411665773194240",

&nbsp;               "keys": {

&nbsp;                   "id": ""

&nbsp;               },

&nbsp;               "number": "1164099",

&nbsp;               "type": "Add"

&nbsp;           }

&nbsp;       \],

&nbsp;       "successCount": "1"

&nbsp;   },

&nbsp;   "errorCode": "0",

&nbsp;   "message": **null**,

&nbsp;   "status": **true**

}
