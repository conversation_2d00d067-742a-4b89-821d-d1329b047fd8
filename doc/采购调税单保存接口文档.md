## 采购调税单保存接口

### 接口描述：

- 广交云.【入库税率变更单】.审核后 **推送** 金蝶云·星空旗舰版.【采购调税单】

**请求 URL**：

{{http/https}}://{{localhost}}/kapi/v2/gjwl/im/gjwl_purtaxadjust/savePurTaxAdjust

**请求 URL（沙箱环境）:**

[https://gjwl.test.kdgalaxy.com/kapi/v2/gjwl/im/gjwl_purtaxadjust/savePurTaxAdjust](https://gjwl.test.kdgalaxy.com/kapi/v2/gjwl/cas/recClaimInterface/recClaimInterface)

**调用方式**：HTTP调用

**请求方式**：POST

**请求类型**：Content-Type:application/json

**请求Header参数：(和 [3.1.客户保存提交接口](#_客户保存提交接口) 请求Header参数的一致)**

**请求Body参数：**

|     |     |     |     |     |     |
| --- | --- | --- | --- | --- | --- |
| **参数名** | **参数类型** | **是否必填** | **描述说明** | **层级** | **参数值** |
| id  | String | 否   | id  | 1   | 修改时需传 |
| billno | String | 是   | 单据编号 | 1   | "1164021" |
| org_number | String | 是   | 组织.编码 | 1   | 固定值，传"1011" |
| gjwl_date | Date | 是   | 业务日期 | 1   | "2025-09-12" |
| gjwl_supplier_number | String | 是   | 供应商.编码 | 1   | "QX000004" |
| gjwl_reason | String | 是   | 税率变更原因 | 1   | "测试" |
| gjwl_thirdparty_billno | String | 是   | 外部系统单号 | 1   | "1164021" |
| gjwl_sourcesystemtype | String | 是   | 来源系统 | 1   | "广交云供应链管理系统" |
| entryentity | Entries | 否   | 物料明细 | 1   | {},{} |
| id  | Long | 否   | 物料明细.id | 2   | 修改时需传 |
| gjwl_purorderbillno | String | 否   | 物料明细.采购单号 | 2   | "3212023" |
| gjwl_instockbillno | String | 否   | 物料明细.入库单号 | 2   | "2129925" |
| gjwl_materiel_number | String | 否   | 物料编码.编码 | 2   | "*********" |
| gjwl_producer | String | 否   | 物料明细.生产厂家/受托生产企业 | 2   | "xxx公司" |
| gjwl_product_lotno | String | 否   | 物料明细.生产批号/序列号 | 2   | "2123" |
| gjwl_warehouse_number | String | 是   | 仓库.编码 | 2   | "CK-001" |
| gjwl_qty | Decimal | 否   | 物料明细.数量 | 2   | "50.00" |
| gjwl_taxprice | Decimal | 否   | 物料明细.采购价（含税 | 2   | "21.23" |
| gjwl_oldtaxrate_number | String | 是   | 原单税率.编码 | 2   | "001005" |
| gjwl_newesttaxrate_number | String | 是   | 调整前最新税率.编码 | 2   | "001005" |
| gjwl_taxrate_number | String | 是   | 本次调整税率.编码 | 2   | "001003" |
| gjwl_hisentity | Entries | 否   | 税率变更金额调整单 | 1   | {},{} |
| id  | Long | 否   | 税率变更金额调整单.id | 2   | 修改时需传 |
| gjwl_purorderbillno_h | String | 是   | 税率变更金额调整单.采购单号 | 2   | "3212023" |
| gjwl_billno_h | String | 是   | 税率变更金额调整单.单据编号 | 2   | "4242048" |
| gjwl_materiel_h_masterid_number | String | 是   | 物料编码.编码 | 2   | "*********" |
| gjwl_product_lotno_h | String | 否   | 税率变更金额调整单.生产批号/序列号 | 2   | "2123" |
| gjwl_taxrate_h_number | String | 是   | 税率.编码 | 2   | "001003" |
| gjwl_qty_h | Decimal | 否   | 税率变更金额调整单.数量 | 2   | "-50" |
| gjwl_taxprice_h | Decimal | 否   | 税率变更金额调整单.单价（含税） | 2   | "21.23" |
| gjwl_adjusttype | String | 否   | 税率变更金额调整单.调整类型 | 2   | "采购入库单(税率变更原单冲回)" |
| gjwl_allamount | Decimal | 否   | 税率变更金额调整单.金额（含税） | 2   | "-1061.5" |
| gjwl_amount | Decimal | 否   | 税率变更金额调整单.金额（不含税） | 2   | "-939.38" |
| gjwl_tax | Decimal | 否   | 税率变更金额调整单.税额 | 2   | "-122.12" |

**请求参数示例（沙箱环境）:**

{

&nbsp;   "data":\[

&nbsp;       {

&nbsp;           "billno":"2212021",

&nbsp;           "org_number":"1011",

&nbsp;           "gjwl_date":"2025-09-12",

&nbsp;           "gjwl_supplier_number":"QX000004",

&nbsp;           "gjwl_reason":"采购调价",

&nbsp;           "gjwl_sourcesystemtype":"广交云供应链管理系统",

&nbsp;           "gjwl_thirdparty_billno":"2212021",

&nbsp;           "entryentity":\[

&nbsp;               {

&nbsp;                   "gjwl_purorderbillno":"3212023",

&nbsp;                   "gjwl_instockbillno":"2129925",

&nbsp;                   "gjwl_materiel_number":"*********",

&nbsp;                   "gjwl_producer":"广东罗浮山国药股份有限公司",

&nbsp;                   "gjwl_product_lotno":"2123",

&nbsp;                   "gjwl_warehouse_number":"CK-001",

&nbsp;                   "gjwl_qty":"50",

&nbsp;                   "gjwl_taxprice":"21.23",

&nbsp;                   "gjwl_oldtaxrate_number":"001005",

&nbsp;                   "gjwl_newesttaxrate_number":"001005",

&nbsp;                   "gjwl_taxrate_number":"001003"

&nbsp;               }

&nbsp;           \],

&nbsp;           "gjwl_hisentity":\[

&nbsp;               {

&nbsp;                   "gjwl_purorderbillno_h":"3212023",

&nbsp;                   "gjwl_billno_h":"yS5Wd",

&nbsp;                   "gjwl_adjusttype":"采购入库单(税率变更原单冲回)",

&nbsp;                   "gjwl_materiel_h_masterid_number":"*********",

&nbsp;                   "gjwl_product_lotno_h":"2123",

&nbsp;                   "gjwl_qty_h":"-50",

&nbsp;                   "gjwl_taxrate_h_number":"001003",

&nbsp;                   "gjwl_taxprice_h":"21.23",

&nbsp;                   "gjwl_allamount":"-1061.5",

&nbsp;                   "gjwl_tax":"-122.12",

&nbsp;                   "gjwl_amount":"-939.38"

&nbsp;               }

&nbsp;           \]

&nbsp;       }

&nbsp;   \]

}  

**返回参数：**

|     |     |     |     |     |
| --- | --- | --- | --- | --- |
| **参数名** | **参数值** | **参数类型** | **层级** | **描述说明** |
| data | {}  | Object | 1   | 结果数据 |
| result | "\[\]" | Array&lt;Map&gt; | 2   | 返回结果详细信息 |
| failCount | "2" | String | 2   | 操作失败数量 |
| successCount | "1" | String | 2   | 操作成功数量 |
| errorCode | 成功时为0，失败时会返回错误码如400 | String | 1   | 错误码 |
| message | 成功时为空，失败时会返回错误信息如“操作失败” | String | 1   | 接口调用错误信息 |
| status | true/false | Boolean | 1   | 接口访问是否成功 |

**返回参数示例:**

{

&nbsp;   "data": {

&nbsp;       "failCount": "0",

&nbsp;       "result": \[

&nbsp;           {

&nbsp;               "billIndex": 0,

&nbsp;               "billStatus": **true**,

&nbsp;               "errors": \[\],

&nbsp;               "id": "2307424504277650432",

&nbsp;               "keys": {

&nbsp;                   "id": ""

&nbsp;               },

&nbsp;               "number": "2212021",

&nbsp;               "type": "Add"

&nbsp;           }

&nbsp;       \],

&nbsp;       "successCount": "1"

&nbsp;   },

&nbsp;   "errorCode": "0",

&nbsp;   "message": **null**,

&nbsp;   "status": **true**

}
