## 返利单保存接口

### 接口描述：

- 广交云.【采购返利单】.审核后 **推送** 金蝶云·星空旗舰版.【返利单】
- 广交云.【销售折让单】.审核后 **推送** 金蝶云·星空旗舰版.【返利单】

**请求 URL**：

{{http/https}}://{{localhost}}/kapi/v2/gjwl/gjwl_rebate/gjwl_rebate/saveRebate

**请求 URL（沙箱环境）:**

[https://gjwl.test.kdgalaxy.com/kapi/v2/gjwl/gjwl_rebate/gjwl_rebate/saveRebate](https://gjwl.test.kdgalaxy.com/kapi/v2/gjwl/cas/recClaimInterface/recClaimInterface)

**调用方式**：HTTP调用

**请求方式**：POST

**请求类型**：Content-Type:application/json

**请求Header参数：(和 [3.1.客户保存提交接口](#_客户保存提交接口) 请求Header参数的一致)**

**请求Body参数：**

|     |     |     |     |     |     |
| --- | --- | --- | --- | --- | --- |
| **参数名** | **参数类型** | **是否必填** | **描述说明** | **层级** | **参数值** |
| id  | String | 否   | id  | 1   | 修改时需传 |
| billno | String | 是   | 确认单号 | 1   | "FLQR2025000087" |
| org_number | String | 是   | 组织.编码 | 1   | 固定值，传"1011" |
| gjwl_billtype_number | String | 是   | 单据类型.编码 | 1   | "5FyQB" |
| gjwl_contactunittype | String | 是   | 往来单位类型 | 1   | "bd_supplier" |
| gjwl_contactunit_number | String | 是   | 往来单位.编码 | 1   | "XcCPY" |
| gjwl_rebateamountsum | Decimal | 否   | 应返利金额 | 1   | "696.07" |
| gjwl_confirmamountsum | Decimal | 否   | 确认返利金额 | 1   | "503.24" |
| gjwl_outsidecratedate | Date | 否   | 外部生成时间 | 1   | "2025-09-25" |
| gjwl_outsideauditdate | Date | 否   | 外部审核时间 | 1   | "2025-09-25" |
| gjwl_remark | String | 否   | 备注  | 1   | "Rcbvx" |
| entryentity | Entries | 否   | 返利明细 | 1   | {},{} |
| id  | Long | 否   | 返利明细.id | 2   | "8508589098364833792" |
| gjwl_rebateamount | Decimal | 否   | 返利明细.应返利金额 | 2   | "413.53" |
| gjwl_confirmamount | Decimal | 否   | 返利明细.确认返利金额 | 2   | "219.93" |
| gjwl_bizdate | Date | 否   | 返利明细.业务日期 | 2   | "2025-09-25" |
| gjwl_mainbillno | String | 否   | 返利明细.核心单号 | 2   | "nW4Ot" |
| gjwl_bizno | String | 否   | 返利明细.业务单据 | 2   | "8wRlr" |
| gjwl_biztype | String | 否   | 返利明细.单据类型 | 2   | "7Xynn" |
| gjwl_protocolno | String | 否   | 返利明细.协议编号 | 2   | "8MDao" |
| gjwl_protocolname | String | 否   | 返利明细.协议名称 | 2   | "gwu9x" |
| gjwl_clauseno | String | 否   | 返利明细.条款编号 | 2   | "bqpuq" |
| gjwl_materiel_number | String | 是   | 物料编码.编码 | 2   | "M6Ty7" |
| gjwl_producer | String | 否   | 返利明细.生产厂家/受托生产企业 | 2   | "G7Dyz" |
| gjwl_qty | Decimal | 否   | 返利明细.数量 | 2   | "934.36" |
| gjwl_price | Decimal | 否   | 返利明细.单价 | 2   | "703.15" |
| gjwl_amount | Decimal | 否   | 返利明细.金额 | 2   | "287.84" |
| gjwl_lotno | String | 否   | 返利明细.批号 | 2   | "BxrAb" |
| gjwl_product_lotno | String | 否   | 返利明细.生产批号/序列号 | 2   | "EXUmC" |
| gjwl_rebatepoint | Decimal | 否   | 返利明细.返利点数 | 2   | "905.14" |
| gjwl_allocateqty | Decimal | 否   | 返利明细.分摊数量 | 2   | "332.37" |
| gjwl_protocoltype | String | 否   | 返利明细.协议分类 | 2   | "Kmmue" |
| gjwl_rebatetype | String | 否   | 返利明细.返利类型 | 2   | "nDRaQ" |
| gjwl_ledgerid | String | 否   | 返利明细.台账ID | 2   | "gpQgW" |
| gjwl_rebatesource | String | 否   | 返利明细.返利来源 | 2   | "lOass" |
| gjwl_rebatepaytype | String | 否   | 返利明细.返利支付方式 | 2   | "dAYOH" |

**请求参数示例（沙箱环境）:**

{

&nbsp;   "data":\[

&nbsp;       {

&nbsp;           "billno":"2212021",

&nbsp;           "org_number":"1011",

&nbsp;           "gjwl_date":"2025-09-12",

&nbsp;           "gjwl_supplier_number":"QX000004",

&nbsp;           "gjwl_reason":"采购调价",

&nbsp;           "gjwl_sourcesystemtype":"广交云供应链管理系统",

&nbsp;           "gjwl_thirdparty_billno":"2212021",

&nbsp;           "entryentity":\[

&nbsp;               {

&nbsp;                   "gjwl_purorderbillno":"3212023",

&nbsp;                   "gjwl_instockbillno":"2129925",

&nbsp;                   "gjwl_materiel_number":"*********",

&nbsp;                   "gjwl_producer":"广东罗浮山国药股份有限公司",

&nbsp;                   "gjwl_product_lotno":"2123",

&nbsp;                   "gjwl_warehouse_number":"CK-001",

&nbsp;                   "gjwl_qty":"50",

&nbsp;                   "gjwl_taxprice":"21.23",

&nbsp;                   "gjwl_oldtaxrate_number":"001005",

&nbsp;                   "gjwl_newesttaxrate_number":"001005",

&nbsp;                   "gjwl_taxrate_number":"001003"

&nbsp;               }

&nbsp;           \],

&nbsp;           "gjwl_hisentity":\[

&nbsp;               {

&nbsp;                   "gjwl_purorderbillno_h":"3212023",

&nbsp;                   "gjwl_billno_h":"yS5Wd",

&nbsp;                   "gjwl_adjusttype":"采购入库单(税率变更原单冲回)",

&nbsp;                   "gjwl_materiel_h_masterid_number":"*********",

&nbsp;                   "gjwl_product_lotno_h":"2123",

&nbsp;                   "gjwl_qty_h":"-50",

&nbsp;                   "gjwl_taxrate_h_number":"001003",

&nbsp;                   "gjwl_taxprice_h":"21.23",

&nbsp;                   "gjwl_allamount":"-1061.5",

&nbsp;                   "gjwl_tax":"-122.12",

&nbsp;                   "gjwl_amount":"-939.38"

&nbsp;               }

&nbsp;           \]

&nbsp;       }

&nbsp;   \]

}  

**返回参数：**

|     |     |     |     |     |
| --- | --- | --- | --- | --- |
| **参数名** | **参数值** | **参数类型** | **层级** | **描述说明** |
| data | {}  | Object | 1   | 结果数据 |
| result | "\[\]" | Array&lt;Map&gt; | 2   | 返回结果详细信息 |
| failCount | "2" | String | 2   | 操作失败数量 |
| successCount | "1" | String | 2   | 操作成功数量 |
| errorCode | 成功时为0，失败时会返回错误码如400 | String | 1   | 错误码 |
| message | 成功时为空，失败时会返回错误信息如“操作失败” | String | 1   | 接口调用错误信息 |
| status | true/false | Boolean | 1   | 接口访问是否成功 |

**返回参数示例:**

{

&nbsp;   "data": {

&nbsp;       "failCount": "0",

&nbsp;       "result": \[

&nbsp;           {

&nbsp;               "billIndex": 0,

&nbsp;               "billStatus": **true**,

&nbsp;               "errors": \[\],

&nbsp;               "id": "2307424504277650432",

&nbsp;               "keys": {

&nbsp;                   "id": ""

&nbsp;               },

&nbsp;               "number": "2212021",

&nbsp;               "type": "Add"

&nbsp;           }

&nbsp;       \],

&nbsp;       "successCount": "1"

&nbsp;   },

&nbsp;   "errorCode": "0",

&nbsp;   "message": **null**,

&nbsp;   "status": **true**

}
