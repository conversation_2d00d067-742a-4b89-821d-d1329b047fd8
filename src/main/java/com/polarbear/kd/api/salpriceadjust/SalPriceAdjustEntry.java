package com.polarbear.kd.api.salpriceadjust;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 销售调价单明细实体类
 * 
 * <AUTHOR>
 * @date 2025-09-23
 */
@Data
public class SalPriceAdjustEntry {
    
    /**
     * 物料明细.id (修改时需传)
     */
    @JsonProperty("id")
    private Long id;
    
    /**
     * 物料明细.销售单号
     */
    @JsonProperty("gjwl_salorderbillno")
    private String gjwlSalorderbillno;
    
    /**
     * 物料明细.单据编号
     */
    @JsonProperty("gjwl_billno")
    private String gjwlBillno;
    
    /**
     * 物料明细.单据类型
     */
    @JsonProperty("gjwl_billtype")
    private String gjwlBilltype;
    
    /**
     * 物料编码.编码
     */
    @JsonProperty("gjwl_materiel_number")
    private String gjwlMaterielNumber;
    
    /**
     * 物料明细.生产批号/序列号
     */
    @JsonProperty("gjwl_product_lotno")
    private String gjwlProductLotno;
    
    /**
     * 税率.编码
     */
    @JsonProperty("gjwl_taxrate_number")
    private String gjwlTaxrateNumber;
    
    /**
     * 物料明细.数量
     */
    @JsonProperty("gjwl_qty")
    private BigDecimal gjwlQty;
    
    /**
     * 物料明细.原单价（含税）
     */
    @JsonProperty("gjwl_oldtaxprice")
    private BigDecimal gjwlOldtaxprice;
    
    /**
     * 物料明细.调整前最新价（含税）
     */
    @JsonProperty("gjwl_newesttaxprice")
    private BigDecimal gjwlNewesttaxprice;
    
    /**
     * 物料明细.本次调整单价（含税）
     */
    @JsonProperty("gjwl_taxprice")
    private BigDecimal gjwlTaxprice;
    
    /**
     * 物料明细.调整金额
     */
    @JsonProperty("gjwl_adjustamount")
    private BigDecimal gjwlAdjustamount;
    
    // 常量定义
    
    /**
     * 单据类型 - 销售出库单
     */
    public static final String BILLTYPE_SALES_OUT = "销售出库单";
    
    /**
     * 单据类型 - 销售订单
     */
    public static final String BILLTYPE_SALES_ORDER = "销售订单";
}
