package com.polarbear.kd.api.salpriceadjust;

import com.polarbear.kd.core.Config;
import com.polarbear.kd.core.KdOpRequest;

import java.util.List;

/**
 * 销售调价单保存接口
 * 
 * <AUTHOR>
 * @apiNote 广交云.【销售调价单】.审核后 推送 金蝶云·星空旗舰版.【销售调价单】
 * @date 2025-09-23
 */
public class SalPriceAdjustSaveRequest extends KdOpRequest<List<SalPriceAdjust>> {

    @Override
    public String getUrlPath() {
        return "/v2/gjwl/im/gjwl_salpriceadjust/saveSalPriceAdjust";
    }

    @Override
    public String logModule() {
        return "kd.gjwl_salpriceadjust.saveSalPriceAdjust";
    }

    @Override
    public Class<SalPriceAdjustSaveResponse> getResponseClass() {
        return SalPriceAdjustSaveResponse.class;
    }

    @Override
    public KdOpRequest<List<SalPriceAdjust>> setLogKey(Config<List<SalPriceAdjust>> config) {
        config.setKey1(o -> {
            String keys = String.join(",", o.stream().map(SalPriceAdjust::getGjwlThirdpartyBillno).toArray(String[]::new));
            return keys.substring(0, Math.min(keys.length(), 250));
        });
        return this;
    }
}
