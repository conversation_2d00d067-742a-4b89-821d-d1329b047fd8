package com.polarbear.kd.api.creditstatus;

import com.polarbear.kd.core.Config;
import com.polarbear.kd.core.KdOpRequest;
import java.util.Set;

/**
 * 查询信用状况接口
 *
 * <AUTHOR>
 * @apiNote 广交云.【销售订单】调用金蝶云·星空旗舰版.【信用状况表接口】查询客户信用状况
 * @date 2025-09-22
 */
public class CreditBalanceRequest extends KdOpRequest<CreditBalanceParam> {

  /** 构造函数 */
  public CreditBalanceRequest() {
    super();
  }

  /**
   * 构造函数，传入客户编码集合
   *
   * @param customerNumbers 客户编码集合
   */
  public CreditBalanceRequest(Set<String> customerNumbers) {
    super();
    this.setParam(new CreditBalanceParam(customerNumbers));
  }

  /**
   * 构造函数，传入查询参数
   *
   * @param param 查询参数
   */
  public CreditBalanceRequest(CreditBalanceParam param) {
    super();
    this.setParam(param);
  }

  @Override
  public String getUrlPath() {
    return "/v2/ccm/credit/getCreditBalance";
  }

  @Override
  public String logModule() {
    return "kd.ccm.credit.getCreditBalance";
  }

  @Override
  public Class<CreditBalanceResponse> getResponseClass() {
    return CreditBalanceResponse.class;
  }

  @Override
  public boolean standard() {
    return false;
  }

  @Override
  public KdOpRequest<CreditBalanceParam> setLogKey(Config<CreditBalanceParam> config) {
    config.setKey1(
        param -> {
          if (param != null
              && param.getRolenumberset0() != null
              && !param.getRolenumberset0().isEmpty()) {
            String keys = String.join(",", param.getRolenumberset0());
            return keys.substring(0, Math.min(keys.length(), 250));
          }
          return "credit_query";
        });
    return this;
  }
}
