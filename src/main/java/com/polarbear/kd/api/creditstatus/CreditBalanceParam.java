package com.polarbear.kd.api.creditstatus;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.Set;
import lombok.Data;

/**
 * 查询信用状况请求参数
 *
 * <AUTHOR>
 * @apiNote 广交云.【销售订单】调用金蝶云·星空旗舰版.【信用状况表接口】查询客户信用状况
 * @date 2025-09-22
 */
@Data
public class CreditBalanceParam {

  /** 币别编码(值为具体的币别编码) 可选参数，默认值"CNY" */
  @JsonProperty("currencynumber")
  private String currencynumber;

  /** 控制组织范围(输入控制范围的编码，如：组织范围：SINGLE；集团范围GLOBAL） 可选参数，固定值，传"SINGLE" */
  @JsonProperty("orgscope")
  private String orgscope;

  /** 授信组织编码集合（输入授信组织对应的业务单元编码） 可选参数，默认值["1011"] */
  @JsonProperty("orgnumberset")
  private Set<String> orgnumberset;

  /** 信控维度编码（如：客户：CUSTOMER；供应商：SUPPLIER） 必填参数，固定值，传"CUSTOMER" */
  @JsonProperty("dimensionnumber")
  private String dimensionnumber;

  /**
   * 维度成员类型0:信用维度成员类型标识（输入维度成员对应的业务对象编码，可在维度成员列表查找。如客户：bd_customer，客户统一码：ccm_cusunicode）
   * 可选参数，固定值，传"bd_customer"
   */
  @JsonProperty("roletype0")
  private String roletype0;

  /** 维度成员0:信用维度对象编码集合（输入维度成员值对应的编码，如果信控维度是客户，则录入具体的客户编码） 可选参数，传入客户编码集合 */
  @JsonProperty("rolenumberset0")
  private Set<String> rolenumberset0;

  /** 信用控制方案编码 可选参数，固定值，传"XKFA-SYS-001" */
  @JsonProperty("schemenumber")
  private String schemenumber;

  /** 构造函数，设置默认值 */
  public CreditBalanceParam() {
    this.currencynumber = "CNY";
    this.orgscope = "SINGLE";
    this.dimensionnumber = "CUSTOMER";
    this.roletype0 = "bd_customer";
    this.schemenumber = "XKFA-SYS-001";
  }

  /**
   * 构造函数，传入客户编码集合
   *
   * @param customerNumbers 客户编码集合
   */
  public CreditBalanceParam(Set<String> customerNumbers) {
    this();
    this.rolenumberset0 = customerNumbers;
  }

  /**
   * 设置授信组织编码集合
   *
   * @param orgNumbers 授信组织编码集合
   * @return 当前对象
   */
  public CreditBalanceParam setOrgNumberSet(Set<String> orgNumbers) {
    this.orgnumberset = orgNumbers;
    return this;
  }

  /**
   * 设置客户编码集合
   *
   * @param customerNumbers 客户编码集合
   * @return 当前对象
   */
  public CreditBalanceParam setCustomerNumbers(Set<String> customerNumbers) {
    this.rolenumberset0 = customerNumbers;
    return this;
  }

  /**
   * 设置币别编码
   *
   * @param currencyNumber 币别编码
   * @return 当前对象
   */
  public CreditBalanceParam setCurrencyNumber(String currencyNumber) {
    this.currencynumber = currencyNumber;
    return this;
  }
}
