package com.polarbear.kd.api.creditstatus;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Set;
import lombok.Data;

/**
 * 查询信用状况响应数据
 *
 * <AUTHOR>
 * @date 2025-09-22
 */
@Data
public class CreditBalanceResponseData {

  /** 信用档案ID */
  @JsonProperty("archiveidSet")
  private Set<Long> archiveidSet;

  /** 控制组织范围 */
  @JsonProperty("orgscope")
  private String orgscope;

  /** 授信组织 */
  @JsonProperty("archiveorgnumberlist")
  private List<String> archiveorgnumberlist;

  /** 信用对象 */
  @JsonProperty("archiveobjectmap")
  private HashMap<String, String> archiveobjectmap;

  /** 信用控制方案 */
  @JsonProperty("schemenumber")
  private String schemenumber;

  /** 信控维度 */
  @JsonProperty("dimensionnumber")
  private String dimensionnumber;

  /** 币别 */
  @JsonProperty("currencynumber")
  private String currencynumber;

  /** 币别隔离 */
  @JsonProperty("singlecurcontrol")
  private Boolean singlecurcontrol;

  /** 信用额度 */
  @JsonProperty("quotaamount")
  private BigDecimal quotaamount;

  /** 临时信用额度 */
  @JsonProperty("tempamount")
  private BigDecimal tempamount;

  /** 实际占用额度 */
  @JsonProperty("occupyamount")
  private BigDecimal occupyamount;

  /** 可用额度 */
  @JsonProperty("balance")
  private BigDecimal balance;

  /** 信用天数 */
  @JsonProperty("quotaoverdays")
  private BigDecimal quotaoverdays;

  /** 临时信用天数 */
  @JsonProperty("tempoverdays")
  private BigDecimal tempoverdays;

  /** 逾期天数 */
  @JsonProperty("actualoverdays")
  private BigDecimal actualoverdays;

  /** 超标逾期天数 */
  @JsonProperty("overdaysbal")
  private BigDecimal overdaysbal;

  /** 允许逾期额度 */
  @JsonProperty("quotaoveramount")
  private BigDecimal quotaoveramount;

  /** 临时逾期额度 */
  @JsonProperty("tempoveramount")
  private BigDecimal tempoveramount;

  /** 实际逾期额度 */
  @JsonProperty("actualoveramount")
  private BigDecimal actualoveramount;

  /** 超标逾期金额 */
  @JsonProperty("overamountbal")
  private BigDecimal overamountbal;

  /** 是否超标 */
  @JsonProperty("exceed")
  private Boolean exceed;

  /** 信用等级 */
  @JsonProperty("grade")
  private String grade;

  /** 额度类型档案ID */
  @JsonProperty("quoarchivemap")
  private HashMap<String, Long> quoarchivemap;

  /** 汇率表 */
  @JsonProperty("exratetablenumber")
  private String exratetablenumber;
}
