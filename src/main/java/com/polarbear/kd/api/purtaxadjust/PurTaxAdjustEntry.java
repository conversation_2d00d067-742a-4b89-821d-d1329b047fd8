package com.polarbear.kd.api.purtaxadjust;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 采购调税单物料明细实体类
 * 
 * <AUTHOR>
 * @date 2025-09-25
 */
@Data
public class PurTaxAdjustEntry {
    
    /**
     * 物料明细.id (修改时需传)
     */
    @JsonProperty("id")
    private Long id;
    
    /**
     * 物料明细.采购单号
     */
    @JsonProperty("gjwl_purorderbillno")
    private String gjwlPurorderbillno;
    
    /**
     * 物料明细.入库单号
     */
    @JsonProperty("gjwl_instockbillno")
    private String gjwlInstockbillno;
    
    /**
     * 物料编码.编码
     */
    @JsonProperty("gjwl_materiel_number")
    private String gjwlMaterielNumber;
    
    /**
     * 物料明细.生产厂家/受托生产企业
     */
    @JsonProperty("gjwl_producer")
    private String gjwlProducer;
    
    /**
     * 物料明细.生产批号/序列号
     */
    @JsonProperty("gjwl_product_lotno")
    private String gjwlProductLotno;
    
    /**
     * 仓库.编码
     */
    @JsonProperty("gjwl_warehouse_number")
    private String gjwlWarehouseNumber;
    
    /**
     * 物料明细.数量
     */
    @JsonProperty("gjwl_qty")
    private BigDecimal gjwlQty;
    
    /**
     * 物料明细.采购价（含税）
     */
    @JsonProperty("gjwl_taxprice")
    private BigDecimal gjwlTaxprice;
    
    /**
     * 原单税率.编码
     */
    @JsonProperty("gjwl_oldtaxrate_number")
    private String gjwlOldtaxrateNumber;
    
    /**
     * 调整前最新税率.编码
     */
    @JsonProperty("gjwl_newesttaxrate_number")
    private String gjwlNewesttaxrateNumber;
    
    /**
     * 本次调整税率.编码
     */
    @JsonProperty("gjwl_taxrate_number")
    private String gjwlTaxrateNumber;
}
