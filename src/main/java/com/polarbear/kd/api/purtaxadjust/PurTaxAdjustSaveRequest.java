package com.polarbear.kd.api.purtaxadjust;

import com.polarbear.kd.core.Config;
import com.polarbear.kd.core.KdOpRequest;

import java.util.List;

/**
 * 采购调税单保存接口
 * 
 * <AUTHOR>
 * @apiNote 广交云.【入库税率变更单】.审核后 推送 金蝶云·星空旗舰版.【采购调税单】
 * @date 2025-09-25
 */
public class PurTaxAdjustSaveRequest extends KdOpRequest<List<PurTaxAdjust>> {

    @Override
    public String getUrlPath() {
        return "/v2/gjwl/im/gjwl_purtaxadjust/savePurTaxAdjust";
    }

    @Override
    public String logModule() {
        return "kd.gjwl_purtaxadjust.savePurTaxAdjust";
    }

    @Override
    public Class<PurTaxAdjustSaveResponse> getResponseClass() {
        return PurTaxAdjustSaveResponse.class;
    }

    @Override
    public KdOpRequest<List<PurTaxAdjust>> setLogKey(Config<List<PurTaxAdjust>> config) {
        config.setKey1(o -> {
            String keys = String.join(",", o.stream().map(PurTaxAdjust::getGjwlThirdpartyBillno).toArray(String[]::new));
            return keys.substring(0, Math.min(keys.length(), 250));
        });
        return this;
    }
}
