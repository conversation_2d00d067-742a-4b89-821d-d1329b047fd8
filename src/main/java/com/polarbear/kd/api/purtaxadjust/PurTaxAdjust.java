package com.polarbear.kd.api.purtaxadjust;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

/**
 * 采购调税单实体类
 * 
 * <AUTHOR>
 * @apiNote 广交云.【入库税率变更单】.审核后 推送 金蝶云·星空旗舰版.【采购调税单】
 * @date 2025-09-25
 */
@Data
public class PurTaxAdjust {
    
    /**
     * id (修改时需传)
     */
    @JsonProperty("id")
    private String id;
    
    /**
     * 单据编号
     */
    @JsonProperty("billno")
    private String billno;
    
    /**
     * 组织.编码 (固定值，传"1011")
     */
    @JsonProperty("org_number")
    private String orgNumber;
    
    /**
     * 业务日期
     */
    @JsonProperty("gjwl_date")
    private String gjwlDate;
    
    /**
     * 供应商.编码
     */
    @JsonProperty("gjwl_supplier_number")
    private String gjwlSupplierNumber;
    
    /**
     * 税率变更原因
     */
    @JsonProperty("gjwl_reason")
    private String gjwlReason;
    
    /**
     * 外部系统单号
     */
    @JsonProperty("gjwl_thirdparty_billno")
    private String gjwlThirdpartyBillno;
    
    /**
     * 来源系统 (固定值，传"广交云供应链管理系统")
     */
    @JsonProperty("gjwl_sourcesystemtype")
    private String gjwlSourcesystemtype;
    
    /**
     * 物料明细
     */
    @JsonProperty("entryentity")
    private List<PurTaxAdjustEntry> entryentity;
    
    /**
     * 税率变更金额调整单
     */
    @JsonProperty("gjwl_hisentity")
    private List<PurTaxAdjustHisEntry> gjwlHisentity;
    
    // 常量定义
    
    /**
     * 组织编码默认值
     */
    public static final String ORG_DEFAULT = "1011";
    
    /**
     * 来源系统默认值
     */
    public static final String SOURCE_SYSTEM = "广交云供应链管理系统";
}
