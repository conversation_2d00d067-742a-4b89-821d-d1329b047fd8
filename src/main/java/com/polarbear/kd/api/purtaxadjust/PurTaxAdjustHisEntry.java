package com.polarbear.kd.api.purtaxadjust;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 采购调税单税率变更金额调整单实体类
 * 
 * <AUTHOR>
 * @date 2025-09-25
 */
@Data
public class PurTaxAdjustHisEntry {
    
    /**
     * 税率变更金额调整单.id (修改时需传)
     */
    @JsonProperty("id")
    private Long id;
    
    /**
     * 税率变更金额调整单.采购单号
     */
    @JsonProperty("gjwl_purorderbillno_h")
    private String gjwlPurorderbillnoH;
    
    /**
     * 税率变更金额调整单.单据编号
     */
    @JsonProperty("gjwl_billno_h")
    private String gjwlBillnoH;
    
    /**
     * 物料编码.编码
     */
    @JsonProperty("gjwl_materiel_h_masterid_number")
    private String gjwlMaterielHMasteridNumber;
    
    /**
     * 税率变更金额调整单.生产批号/序列号
     */
    @JsonProperty("gjwl_product_lotno_h")
    private String gjwlProductLotnoH;
    
    /**
     * 税率.编码
     */
    @JsonProperty("gjwl_taxrate_h_number")
    private String gjwlTaxrateHNumber;
    
    /**
     * 税率变更金额调整单.数量
     */
    @JsonProperty("gjwl_qty_h")
    private BigDecimal gjwlQtyH;
    
    /**
     * 税率变更金额调整单.单价（含税）
     */
    @JsonProperty("gjwl_taxprice_h")
    private BigDecimal gjwlTaxpriceH;
    
    /**
     * 税率变更金额调整单.调整类型
     */
    @JsonProperty("gjwl_adjusttype")
    private String gjwlAdjusttype;
    
    /**
     * 税率变更金额调整单.金额（含税）
     */
    @JsonProperty("gjwl_allamount")
    private BigDecimal gjwlAllamount;
    
    /**
     * 税率变更金额调整单.金额（不含税）
     */
    @JsonProperty("gjwl_amount")
    private BigDecimal gjwlAmount;
    
    /**
     * 税率变更金额调整单.税额
     */
    @JsonProperty("gjwl_tax")
    private BigDecimal gjwlTax;
    
    // 常量定义
    
    /**
     * 调整类型 - 采购入库单(税率变更原单冲回)
     */
    public static final String ADJUST_TYPE_INSTOCK_ROLLBACK = "采购入库单(税率变更原单冲回)";
}
