package com.polarbear.kd.api.purpriceadjust;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.math.BigDecimal;
import lombok.Data;

/**
 * 采购调价单明细实体类
 * 
 * <AUTHOR>
 * @date 2025-09-25
 */
@Data
public class PurPriceAdjustEntry {
    
    /**
     * 物料明细.id (修改时需传)
     */
    @JsonProperty("id")
    private Long id;

    /**
    * 采购入库单号
    */
    @JsonProperty("gjwl_instockno")
    private String gjwlInstockno;

    /**
     * 物料编码.编码
     */
    @JsonProperty("gjwl_materiel_masterid_number")
    private String gjwlMaterielMasteridNumber;

    /**
     * 物料明细.生产厂家/受托生产企业
     */
    @JsonProperty("gjwl_producer")
    private String gjwlProducer;

    /**
     * 物料明细.数量
     */
    @JsonProperty("gjwl_qty")
    private BigDecimal gjwlQty;

    /**
     * 物料明细.批号 (对应广交云的批次)
     */
    @JsonProperty("gjwl_lotno")
    private String gjwlLotno;

    /**
     * 物料明细.生产批号/序列号
     */
    @JsonProperty("gjwl_product_lotno")
    private String gjwlProductLotno;

    /**
     * 物料明细.采购价
     */
    @JsonProperty("gjwl_price")
    private BigDecimal gjwlPrice;

    /**
     * 物料明细.不含税金额
     */
    @JsonProperty("gjwl_untaxamount")
    private BigDecimal gjwlUntaxamount;

    /**
     * 物料明细.税额
     */
    @JsonProperty("gjwl_tax")
    private BigDecimal gjwlTax;

    /**
     * 物料明细.发票号码
     */
    @JsonProperty("gjwl_invoiceno")
    private String gjwlInvoiceno;

    /**
     * 物料明细.当前退货数量
     */
    @JsonProperty("gjwl_returnqty")
    private BigDecimal gjwlReturnqty;

    /**
     * 物料明细.调整后采购价
     */
    @JsonProperty("gjwl_afterprice")
    private BigDecimal gjwlAfterprice;

    /**
     * 物料明细.入库调整差额
     */
    @JsonProperty("gjwl_instockdiffamount")
    private BigDecimal gjwlInstockdiffamount;

    /**
     * 物料明细.采退调差金额
     */
    @JsonProperty("gjwl_returndiffamount")
    private BigDecimal gjwlReturndiffamount;

    /**
     * 物料明细.实际调整差额
     */
    @JsonProperty("gjwl_realdiffamount")
    private BigDecimal gjwlRealdiffamount;
    
    // 常量定义

    /**
     * 默认退货数量
     */
    public static final BigDecimal DEFAULT_RETURN_QTY = BigDecimal.ZERO;

    /**
     * 默认不含税金额
     */
    public static final BigDecimal DEFAULT_UNTAX_AMOUNT = BigDecimal.ZERO;

    /**
     * 默认税额
     */
    public static final BigDecimal DEFAULT_TAX = BigDecimal.ZERO;
}
