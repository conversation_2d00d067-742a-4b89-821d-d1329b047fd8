package com.polarbear.kd.api.purpriceadjust;

import com.polarbear.kd.core.Config;
import com.polarbear.kd.core.KdOpRequest;

import java.util.List;

/**
 * 采购调价单保存接口
 * 
 * <AUTHOR>
 * @apiNote 广交云.【入库调价单】.审核后 推送 金蝶云·星空旗舰版.【采购调价单】
 * @date 2025-09-25
 */
public class PurPriceAdjustSaveRequest extends KdOpRequest<List<PurPriceAdjust>> {

    @Override
    public String getUrlPath() {
        return "/v2/gjwl/im/gjwl_purpriceadjust/savePurPriceAdjust";
    }

    @Override
    public String logModule() {
        return "kd.gjwl_purpriceadjust.savePurPriceAdjust";
    }

    @Override
    public Class<PurPriceAdjustSaveResponse> getResponseClass() {
        return PurPriceAdjustSaveResponse.class;
    }
}
